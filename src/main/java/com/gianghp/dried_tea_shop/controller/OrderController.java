package com.gianghp.dried_tea_shop.controller;

import com.gianghp.dried_tea_shop.common.enums.ErrorCode;
import com.gianghp.dried_tea_shop.common.response.ApiPageResponse;
import com.gianghp.dried_tea_shop.common.response.ApiResponse;
import com.gianghp.dried_tea_shop.dto.order.CreateOrderRequestDto;
import com.gianghp.dried_tea_shop.dto.order.OrderResponseDto;
import com.gianghp.dried_tea_shop.dto.order.UpdateOrderStatusRequestDto;
import com.gianghp.dried_tea_shop.service.OrderService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.UUID;

@RestController
@RequiredArgsConstructor
@Slf4j
public class OrderController {

    private final OrderService orderService;

    @GetMapping("/orders")
    public ResponseEntity<ApiPageResponse<OrderResponseDto>> getUserOrders(
        @RequestParam UUID userId, // In real app, this would come from authentication
        @RequestParam(required = false, defaultValue = "0") Integer page,
        @RequestParam(required = false, defaultValue = "10") Integer size
    ) {
        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<OrderResponseDto> orders = orderService.getOrdersByUserId(userId, pageable);
            return ResponseEntity.ok(ApiPageResponse.success(
                orders, "Orders retrieved successfully"
            ));
        } catch (Exception e) {
            log.error("Error occurred: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                .body(ApiPageResponse.error(ErrorCode.INTERNAL_SERVER_ERROR));
        }
    }

    @PostMapping("/orders")
    public ResponseEntity<ApiResponse<OrderResponseDto>> createOrder(
        @RequestParam UUID userId, // In real app, this would come from authentication
        @RequestBody CreateOrderRequestDto createOrderRequestDto
    ) {
        try {
            OrderResponseDto order = orderService.createOrder(userId, createOrderRequestDto);
            return ResponseEntity.ok(ApiResponse.success(
                order, "Order created successfully"
            ));
        } catch (Exception e) {
            log.error("Error occurred: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                .body(ApiResponse.error(ErrorCode.INTERNAL_SERVER_ERROR));
        }
    }

    @GetMapping("/orders/{id}")
    public ResponseEntity<ApiResponse<OrderResponseDto>> getOrderById(
        @PathVariable UUID id
    ) {
        try {
            OrderResponseDto order = orderService.getOrderById(id);
            return ResponseEntity.ok(ApiResponse.success(
                order, "Order retrieved successfully"
            ));
        } catch (Exception e) {
            log.error("Error occurred: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                .body(ApiResponse.error(ErrorCode.INTERNAL_SERVER_ERROR));
        }
    }

    @PutMapping("/orders/{id}/status")
    public ResponseEntity<ApiResponse<OrderResponseDto>> updateOrderStatus(
        @PathVariable UUID id,
        @RequestBody UpdateOrderStatusRequestDto updateOrderStatusRequestDto
    ) {
        try {
            OrderResponseDto order = orderService.updateOrderStatus(id, updateOrderStatusRequestDto);
            return ResponseEntity.ok(ApiResponse.success(
                order, "Order status updated successfully"
            ));
        } catch (Exception e) {
            log.error("Error occurred: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                .body(ApiResponse.error(ErrorCode.INTERNAL_SERVER_ERROR));
        }
    }

    @GetMapping("/admin/orders")
    public ResponseEntity<ApiPageResponse<OrderResponseDto>> getAllOrders(
        @RequestParam(required = false, defaultValue = "0") Integer page,
        @RequestParam(required = false, defaultValue = "10") Integer size
    ) {
        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<OrderResponseDto> orders = orderService.getAllOrders(pageable);
            return ResponseEntity.ok(ApiPageResponse.success(
                orders, "All orders retrieved successfully"
            ));
        } catch (Exception e) {
            log.error("Error occurred: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                .body(ApiPageResponse.error(ErrorCode.INTERNAL_SERVER_ERROR));
        }
    }
}
