package com.gianghp.dried_tea_shop.repository;

import com.gianghp.dried_tea_shop.entity.CartItem;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface CartItemRepository extends JpaRepository<CartItem, UUID> {
    
    /**
     * Find cart items by cart ID
     */
    List<CartItem> findByCartId(UUID cartId);
    
    /**
     * Find cart items by product ID
     */
    List<CartItem> findByProductId(UUID productId);
    
    /**
     * Find cart item by cart and product
     */
    Optional<CartItem> findByCartIdAndProductId(UUID cartId, UUID productId);
    
    /**
     * Check if cart item exists for cart and product
     */
    boolean existsByCartIdAndProductId(UUID cartId, UUID productId);

    /**
     * Delete cart items by cart ID
     */
    void deleteByCartId(UUID cartId);

    /**
     * Delete cart items by product ID
     */
    void deleteByProductId(UUID productId);

    /**
     * Count cart items by cart ID
     */
    long countByCartId(UUID cartId);

    // Removed complex queries - will be handled by service layer if needed
}
