package com.gianghp.dried_tea_shop.repository;

import com.gianghp.dried_tea_shop.entity.Cart;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface CartRepository extends JpaRepository<Cart, UUID> {
    
    /**
     * Find cart by user ID
     */
    Optional<Cart> findByUserId(UUID userId);
    
    /**
     * Check if cart exists for user
     */
    boolean existsByUserId(UUID userId);
    
    // Removed complex queries - will be handled by service layer if needed
}
