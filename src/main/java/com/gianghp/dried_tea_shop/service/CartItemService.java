package com.gianghp.dried_tea_shop.service;

import com.gianghp.dried_tea_shop.dto.cart.AddCartItemRequestDto;
import com.gianghp.dried_tea_shop.dto.cart.AddCartItemResponseDto;
import com.gianghp.dried_tea_shop.dto.cart.UpdateCartItemRequestDto;
import com.gianghp.dried_tea_shop.entity.Cart;
import com.gianghp.dried_tea_shop.entity.CartItem;
import com.gianghp.dried_tea_shop.entity.Product;
import com.gianghp.dried_tea_shop.mapper.CartMapper;
import com.gianghp.dried_tea_shop.repository.CartItemRepository;
import com.gianghp.dried_tea_shop.repository.CartRepository;
import com.gianghp.dried_tea_shop.repository.ProductRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.UUID;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class CartItemService {

    private final CartItemRepository cartItemRepository;
    private final CartRepository cartRepository;
    private final ProductRepository productRepository;
    private final CartMapper cartMapper;

    public AddCartItemResponseDto addItemToCart(UUID userId, AddCartItemRequestDto addCartItemRequestDto) {
        // Get or create cart for user
        Cart cart = cartRepository.findByUserId(userId)
            .orElseThrow(() -> new RuntimeException("Cart not found"));

        Product product = productRepository.findById(UUID.fromString(addCartItemRequestDto.getProductId()))
            .orElseThrow(() -> new RuntimeException("Product not found"));

        // Check if item already exists in cart
        CartItem existingCartItem = cartItemRepository.findByCartIdAndProductId(cart.getId(), product.getId())
            .orElse(null);

        if (existingCartItem != null) {
            // Update quantity
            existingCartItem.setQuantity(existingCartItem.getQuantity() + addCartItemRequestDto.getQuantity());
            CartItem savedCartItem = cartItemRepository.save(existingCartItem);
            return cartMapper.toAddCartItemResponseDto(savedCartItem);
        } else {
            // Create new cart item
            CartItem cartItem = cartMapper.toEntity(addCartItemRequestDto);
            cartItem.setCart(cart);
            cartItem.setProduct(product);

            CartItem savedCartItem = cartItemRepository.save(cartItem);
            return cartMapper.toAddCartItemResponseDto(savedCartItem);
        }
    }

    public AddCartItemResponseDto updateCartItemQuantity(UUID cartItemId, UpdateCartItemRequestDto updateCartItemRequestDto) {
        CartItem cartItem = cartItemRepository.findById(cartItemId)
            .orElseThrow(() -> new RuntimeException("Cart item not found"));

        cartMapper.updateEntityFromDto(updateCartItemRequestDto, cartItem);
        CartItem savedCartItem = cartItemRepository.save(cartItem);

        return cartMapper.toAddCartItemResponseDto(savedCartItem);
    }
}
