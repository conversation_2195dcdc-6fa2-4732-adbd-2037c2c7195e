package com.gianghp.dried_tea_shop.service;

import com.gianghp.dried_tea_shop.dto.cart.CartItemDto;
import com.gianghp.dried_tea_shop.dto.cart.CartResponseDto;
import com.gianghp.dried_tea_shop.entity.Cart;
import com.gianghp.dried_tea_shop.entity.CartItem;
import com.gianghp.dried_tea_shop.entity.User;
import com.gianghp.dried_tea_shop.mapper.CartMapper;
import com.gianghp.dried_tea_shop.repository.CartItemRepository;
import com.gianghp.dried_tea_shop.repository.CartRepository;
import com.gianghp.dried_tea_shop.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class CartService {

    private final CartRepository cartRepository;
    private final CartItemRepository cartItemRepository;
    private final CartMapper cartMapper;
    private final UserRepository userRepository;

    public CartResponseDto getCartByUserId(UUID userId) {
        Cart cart = cartRepository.findByUserId(userId)
            .orElseGet(() -> createCartForUser(userId));

        CartResponseDto cartResponse = cartMapper.toResponseDto(cart);

        // Get cart items
        List<CartItem> cartItems = cartItemRepository.findByCartId(cart.getId());
        List<CartItemDto> cartItemDtos = cartItems.stream()
            .map(cartMapper::toCartItemDto)
            .toList();

        cartResponse.setItems(cartItemDtos);
        return cartResponse;
    }

    private Cart createCartForUser(UUID userId) {
        User user = userRepository.findById(userId)
            .orElseThrow(() -> new RuntimeException("User not found"));

        Cart cart = new Cart();
        cart.setUser(user);
        return cartRepository.save(cart);
    }
}
