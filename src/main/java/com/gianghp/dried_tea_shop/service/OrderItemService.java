package com.gianghp.dried_tea_shop.service;

import com.gianghp.dried_tea_shop.entity.OrderItem;
import com.gianghp.dried_tea_shop.repository.OrderItemRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class OrderItemService {
    
    private final OrderItemRepository orderItemRepository;
    
    // TODO: Implement order item service methods
    // - Create order items from cart items
    // - Find order items by order ID
    // - Find order items by product ID
    // - Find order items by user ID
    // - Calculate order item total
    // - Update order item quantity
    // - Validate order item
    // - Get best selling products
    // - Get top revenue products
    // - Calculate product sales statistics
    // - Generate sales report
}
