package com.gianghp.dried_tea_shop.service;

import com.gianghp.dried_tea_shop.dto.order.CreateOrderRequestDto;
import com.gianghp.dried_tea_shop.dto.order.OrderItemResponseDto;
import com.gianghp.dried_tea_shop.dto.order.OrderResponseDto;
import com.gianghp.dried_tea_shop.dto.order.UpdateOrderStatusRequestDto;
import com.gianghp.dried_tea_shop.entity.Order;
import com.gianghp.dried_tea_shop.entity.OrderItem;
import com.gianghp.dried_tea_shop.entity.Product;
import com.gianghp.dried_tea_shop.entity.User;
import com.gianghp.dried_tea_shop.mapper.OrderMapper;
import com.gianghp.dried_tea_shop.repository.OrderItemRepository;
import com.gianghp.dried_tea_shop.repository.OrderRepository;
import com.gianghp.dried_tea_shop.repository.ProductRepository;
import com.gianghp.dried_tea_shop.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class OrderService {

    private final OrderRepository orderRepository;
    private final OrderItemRepository orderItemRepository;
    private final OrderMapper orderMapper;
    private final UserRepository userRepository;
    private final ProductRepository productRepository;

    public Page<OrderResponseDto> getOrdersByUserId(UUID userId, Pageable pageable) {
        return orderRepository.findByUserId(userId, pageable)
            .map(this::mapToOrderResponseDto);
    }

    public Page<OrderResponseDto> getAllOrders(Pageable pageable) {
        return orderRepository.findAll(pageable)
            .map(this::mapToOrderResponseDto);
    }

    public OrderResponseDto getOrderById(UUID orderId) {
        Order order = orderRepository.findById(orderId)
            .orElseThrow(() -> new RuntimeException("Order not found"));
        return mapToOrderResponseDto(order);
    }

    public OrderResponseDto createOrder(UUID userId, CreateOrderRequestDto createOrderRequestDto) {
        User user = userRepository.findById(userId)
            .orElseThrow(() -> new RuntimeException("User not found"));

        Order order = orderMapper.toEntity(createOrderRequestDto);
        order.setUser(user);

        // Calculate total amount
        BigDecimal totalAmount = BigDecimal.ZERO;
        for (var itemDto : createOrderRequestDto.getItems()) {
            Product product = productRepository.findById(UUID.fromString(itemDto.getProductId()))
                .orElseThrow(() -> new RuntimeException("Product not found"));
            BigDecimal itemTotal = product.getPrice().multiply(BigDecimal.valueOf(itemDto.getQuantity()));
            totalAmount = totalAmount.add(itemTotal);
        }
        order.setTotalAmount(totalAmount);

        Order savedOrder = orderRepository.save(order);

        // Create order items
        for (var itemDto : createOrderRequestDto.getItems()) {
            Product product = productRepository.findById(UUID.fromString(itemDto.getProductId()))
                .orElseThrow(() -> new RuntimeException("Product not found"));

            OrderItem orderItem = orderMapper.toOrderItemEntity(itemDto);
            orderItem.setOrder(savedOrder);
            orderItem.setProduct(product);
            orderItem.setUnitPrice(product.getPrice());

            orderItemRepository.save(orderItem);
        }

        return mapToOrderResponseDto(savedOrder);
    }

    public OrderResponseDto updateOrderStatus(UUID orderId, UpdateOrderStatusRequestDto updateOrderStatusRequestDto) {
        Order order = orderRepository.findById(orderId)
            .orElseThrow(() -> new RuntimeException("Order not found"));

        orderMapper.updateEntityFromDto(updateOrderStatusRequestDto, order);
        Order savedOrder = orderRepository.save(order);

        return mapToOrderResponseDto(savedOrder);
    }

    private OrderResponseDto mapToOrderResponseDto(Order order) {
        OrderResponseDto orderResponse = orderMapper.toResponseDto(order);

        // Get order items
        List<OrderItem> orderItems = orderItemRepository.findByOrderId(order.getId());
        List<OrderItemResponseDto> orderItemDtos = orderItems.stream()
            .map(orderMapper::toOrderItemResponseDto)
            .toList();

        orderResponse.setItems(orderItemDtos);
        return orderResponse;
    }
}
