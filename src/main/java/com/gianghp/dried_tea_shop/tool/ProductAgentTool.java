package com.gianghp.dried_tea_shop.tool;

import com.gianghp.dried_tea_shop.dto.category.CategoryResponseDto;
import com.gianghp.dried_tea_shop.dto.product.ProductResponseDto;
import com.gianghp.dried_tea_shop.mapper.CategoryMapper;
import com.gianghp.dried_tea_shop.mapper.ProductMapper;
import com.gianghp.dried_tea_shop.repository.CategoryRepository;
import com.gianghp.dried_tea_shop.repository.ProductRepository;
import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class ProductAgentTool {

  private final ProductRepository productRepository;
  private final CategoryRepository categoryRepository;
  private final CategoryMapper categoryMapper;
  private final ProductMapper productMapper;

  public List<ProductResponseDto> getProductList(String keyword,
      String status,
      BigDecimal minPrice,
      BigDecimal maxPrice,
      Integer minReview,
      UUID categoryId,
      int limit) {

    return productRepository.searchProducts(keyword, status, minPrice, maxPrice, minReview,
            categoryId,
            limit)
        .stream()
        .map(productMapper::toProductResponseDto)
        .toList();
  }

  public List<CategoryResponseDto> getCategoryList() {
    return categoryRepository.findAll().stream()
        .map(categoryMapper::toResponseDto)
        .toList();
  }

}
